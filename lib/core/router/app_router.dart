import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../features/auth/providers/auth_notifier.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/discovery/screens/home_screen.dart';
import '../../features/search/screens/search_screen.dart';
import '../../features/profile/screens/profile_screen.dart';
import '../../features/profile/screens/favorites_screen.dart';
import '../../features/business/screens/business_detail_screen.dart';
import '../../shared/widgets/main_shell.dart';

class AppRouter {
  static GoRouter createRouter() {
    return GoRouter(
      initialLocation: '/login',
      redirect: (context, state) {
        final authNotifier = context.read<AuthNotifier>();
        final isLoggedIn = authNotifier.token != null;
        final isLoggingIn =
            state.matchedLocation == '/login' ||
            state.matchedLocation == '/register';

        // If not logged in and trying to access protected routes, redirect to login
        if (!isLoggedIn && !isLoggingIn) {
          return '/login';
        }

        // If logged in and trying to access auth routes, redirect to home
        if (isLoggedIn && isLoggingIn) {
          return '/home';
        }

        return null; // No redirect needed
      },
      routes: [
        // Auth routes (outside shell)
        GoRoute(
          path: '/login',
          builder: (context, state) => const LoginScreen(),
        ),
        GoRoute(
          path: '/register',
          builder: (context, state) => const RegisterScreen(),
        ),

        // Business detail route (outside shell)
        GoRoute(
          path: '/business/:slug',
          builder: (context, state) {
            final slug = state.pathParameters['slug']!;
            return BusinessDetailScreen(slug: slug);
          },
        ),

        // Main shell with bottom navigation
        ShellRoute(
          builder: (context, state, child) => MainShell(child: child),
          routes: [
            GoRoute(
              path: '/home',
              builder: (context, state) => const HomeScreen(),
            ),
            GoRoute(
              path: '/search',
              builder: (context, state) => const SearchScreen(),
            ),
            GoRoute(
              path: '/map',
              builder: (context, state) => const Placeholder(
                child: Center(child: Text('Map Screen - Coming Soon')),
              ),
            ),
            GoRoute(
              path: '/favorites',
              builder: (context, state) => const FavoritesScreen(),
            ),
            GoRoute(
              path: '/profile',
              builder: (context, state) => const ProfileScreen(),
            ),
          ],
        ),
      ],
    );
  }
}
