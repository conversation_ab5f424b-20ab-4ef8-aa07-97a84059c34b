import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../providers/favorites_notifier.dart';
import '../../business/domain/business_summary_model.dart';
import '../../../shared/widgets/enhanced_business_card.dart';
import '../../../shared/widgets/enhanced_search_bar.dart';
import '../widgets/favorites_sort_sheet.dart';
import '../widgets/favorites_filter_sheet.dart';
import '../../../core/theme/app_theme.dart';

class EnhancedFavoritesScreen extends StatefulWidget {
  const EnhancedFavoritesScreen({super.key});

  @override
  State<EnhancedFavoritesScreen> createState() => _EnhancedFavoritesScreenState();
}

class _EnhancedFavoritesScreenState extends State<EnhancedFavoritesScreen> {
  List<BusinessSummaryModel> _favoriteBusinesses = [];
  List<BusinessSummaryModel> _filteredBusinesses = [];
  bool _isLoading = true;
  String? _errorMessage;
  String _searchQuery = '';
  String _sortBy = 'recent'; // recent, name, rating, distance
  Set<String> _selectedCategories = {};
  double? _minRating;
  bool _isGridView = false;

  @override
  void initState() {
    super.initState();
    _loadFavorites();
  }

  Future<void> _loadFavorites() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final favoritesNotifier = context.read<FavoritesNotifier>();
      final businesses = await favoritesNotifier.getFavoriteBusinesses();

      if (mounted) {
        setState(() {
          _favoriteBusinesses = businesses;
          _isLoading = false;
        });
        _applyFiltersAndSort();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  void _applyFiltersAndSort() {
    var filtered = List<BusinessSummaryModel>.from(_favoriteBusinesses);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((business) {
        return business.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               (business.primaryCategory?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
      }).toList();
    }

    // Apply category filter
    if (_selectedCategories.isNotEmpty) {
      filtered = filtered.where((business) {
        return business.primaryCategory != null &&
               _selectedCategories.contains(business.primaryCategory);
      }).toList();
    }

    // Apply rating filter
    if (_minRating != null) {
      filtered = filtered.where((business) {
        return business.averageRating >= _minRating!;
      }).toList();
    }

    // Apply sorting
    switch (_sortBy) {
      case 'name':
        filtered.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'rating':
        filtered.sort((a, b) => b.averageRating.compareTo(a.averageRating));
        break;
      case 'distance':
        filtered.sort((a, b) {
          if (a.distanceKm == null && b.distanceKm == null) return 0;
          if (a.distanceKm == null) return 1;
          if (b.distanceKm == null) return -1;
          return a.distanceKm!.compareTo(b.distanceKm!);
        });
        break;
      case 'recent':
      default:
        // Keep original order (most recently added first)
        break;
    }

    setState(() {
      _filteredBusinesses = filtered;
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _applyFiltersAndSort();
  }

  void _showSortSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => FavoritesSortSheet(
        currentSort: _sortBy,
        onSortChanged: (sortBy) {
          setState(() {
            _sortBy = sortBy;
          });
          _applyFiltersAndSort();
        },
      ),
    );
  }

  void _showFilterSheet() {
    final availableCategories = _favoriteBusinesses
        .where((b) => b.primaryCategory != null)
        .map((b) => b.primaryCategory!)
        .toSet()
        .toList();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FavoritesFilterSheet(
        availableCategories: availableCategories,
        selectedCategories: _selectedCategories,
        minRating: _minRating,
        onFiltersChanged: (categories, rating) {
          setState(() {
            _selectedCategories = categories;
            _minRating = rating;
          });
          _applyFiltersAndSort();
        },
      ),
    );
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _selectedCategories.clear();
      _minRating = null;
      _sortBy = 'recent';
    });
    _applyFiltersAndSort();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.gray100,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        title: Text(
          'Favorites',
          style: AppTheme.h3.copyWith(color: AppTheme.gray900),
        ),
        actions: [
          // View toggle
          IconButton(
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
            icon: Icon(
              _isGridView ? FeatherIcons.list : FeatherIcons.grid,
              color: AppTheme.gray700,
            ),
          ),
          // More options
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'clear_filters':
                  _clearFilters();
                  break;
                case 'refresh':
                  _loadFavorites();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear_filters',
                child: Text('Clear Filters'),
              ),
              const PopupMenuItem(
                value: 'refresh',
                child: Text('Refresh'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and filter bar
          Container(
            color: AppTheme.white,
            padding: const EdgeInsets.all(AppTheme.lg),
            child: Column(
              children: [
                // Search bar
                EnhancedSearchBar(
                  hintText: 'Search favorites...',
                  onChanged: _onSearchChanged,
                  initialValue: _searchQuery,
                ),
                
                const SizedBox(height: AppTheme.md),
                
                // Filter and sort buttons
                Row(
                  children: [
                    // Sort button
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _showSortSheet,
                        icon: const Icon(FeatherIcons.arrowUpDown, size: 16),
                        label: Text(_getSortLabel()),
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: AppTheme.gray300),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppTheme.sm),
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(width: AppTheme.sm),
                    
                    // Filter button
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _showFilterSheet,
                        icon: Icon(
                          FeatherIcons.filter,
                          size: 16,
                          color: _hasActiveFilters() ? AppTheme.primaryPurple : null,
                        ),
                        label: Text(
                          'Filter',
                          style: TextStyle(
                            color: _hasActiveFilters() ? AppTheme.primaryPurple : null,
                          ),
                        ),
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(
                            color: _hasActiveFilters() ? AppTheme.primaryPurple : AppTheme.gray300,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(AppTheme.sm),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                
                // Active filters display
                if (_hasActiveFilters()) ...[
                  const SizedBox(height: AppTheme.sm),
                  _buildActiveFilters(),
                ],
              ],
            ),
          ),

          // Results count
          Container(
            color: AppTheme.white,
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.lg,
              vertical: AppTheme.sm,
            ),
            child: Row(
              children: [
                Text(
                  '${_filteredBusinesses.length} ${_filteredBusinesses.length == 1 ? 'favorite' : 'favorites'}',
                  style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray600),
                ),
                if (_filteredBusinesses.length != _favoriteBusinesses.length) ...[
                  Text(
                    ' of ${_favoriteBusinesses.length}',
                    style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
                  ),
                ],
              ],
            ),
          ),

          // Content
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  String _getSortLabel() {
    switch (_sortBy) {
      case 'name':
        return 'Name';
      case 'rating':
        return 'Rating';
      case 'distance':
        return 'Distance';
      case 'recent':
      default:
        return 'Recent';
    }
  }

  bool _hasActiveFilters() {
    return _selectedCategories.isNotEmpty || _minRating != null;
  }

  Widget _buildActiveFilters() {
    return Wrap(
      spacing: AppTheme.xs,
      runSpacing: AppTheme.xs,
      children: [
        // Category filters
        ..._selectedCategories.map((category) {
          return Chip(
            label: Text(category),
            onDeleted: () {
              setState(() {
                _selectedCategories.remove(category);
              });
              _applyFiltersAndSort();
            },
            backgroundColor: AppTheme.primaryPurple.withOpacity(0.1),
            labelStyle: AppTheme.bodySmall.copyWith(
              color: AppTheme.primaryPurple,
              fontWeight: FontWeight.w500,
            ),
            deleteIconColor: AppTheme.primaryPurple,
          );
        }),
        
        // Rating filter
        if (_minRating != null)
          Chip(
            label: Text('${_minRating!.toStringAsFixed(1)}+ stars'),
            onDeleted: () {
              setState(() {
                _minRating = null;
              });
              _applyFiltersAndSort();
            },
            backgroundColor: AppTheme.goldStar.withOpacity(0.1),
            labelStyle: AppTheme.bodySmall.copyWith(
              color: AppTheme.goldStar,
              fontWeight: FontWeight.w500,
            ),
            deleteIconColor: AppTheme.goldStar,
          ),
      ],
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: AppTheme.primaryPurple),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.xxxl),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                FeatherIcons.alertCircle,
                size: 64,
                color: AppTheme.errorRed,
              ),
              const SizedBox(height: AppTheme.lg),
              Text(
                'Failed to load favorites',
                style: AppTheme.h4.copyWith(color: AppTheme.errorRed),
              ),
              const SizedBox(height: AppTheme.sm),
              Text(
                _errorMessage!,
                style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppTheme.lg),
              ElevatedButton(
                onPressed: _loadFavorites,
                child: const Text('Try Again'),
              ),
            ],
          ),
        ),
      );
    }

    if (_favoriteBusinesses.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.xxxl),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                FeatherIcons.heart,
                size: 64,
                color: AppTheme.gray400,
              ),
              const SizedBox(height: AppTheme.lg),
              Text(
                'No favorites yet',
                style: AppTheme.h4.copyWith(color: AppTheme.gray700),
              ),
              const SizedBox(height: AppTheme.sm),
              Text(
                'Start exploring and save your favorite businesses',
                style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    if (_filteredBusinesses.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.xxxl),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                FeatherIcons.search,
                size: 64,
                color: AppTheme.gray400,
              ),
              const SizedBox(height: AppTheme.lg),
              Text(
                'No results found',
                style: AppTheme.h4.copyWith(color: AppTheme.gray700),
              ),
              const SizedBox(height: AppTheme.sm),
              Text(
                'Try adjusting your search or filters',
                style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppTheme.lg),
              ElevatedButton(
                onPressed: _clearFilters,
                child: const Text('Clear Filters'),
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadFavorites,
      child: _isGridView ? _buildGridView() : _buildListView(),
    );
  }

  Widget _buildListView() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.lg),
      itemCount: _filteredBusinesses.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: AppTheme.md),
          child: EnhancedBusinessCard(
            business: _filteredBusinesses[index],
            showDistance: true,
          ),
        );
      },
    );
  }

  Widget _buildGridView() {
    return GridView.builder(
      padding: const EdgeInsets.all(AppTheme.lg),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: AppTheme.md,
        mainAxisSpacing: AppTheme.md,
      ),
      itemCount: _filteredBusinesses.length,
      itemBuilder: (context, index) {
        return EnhancedBusinessCard(
          business: _filteredBusinesses[index],
          isCompact: true,
          showDistance: true,
        );
      },
    );
  }
}
