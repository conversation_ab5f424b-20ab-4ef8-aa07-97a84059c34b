import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../providers/search_notifier.dart';
import '../domain/search_filter_model.dart';
import '../widgets/filter_bottom_sheet.dart';
import '../widgets/sort_bottom_sheet.dart';
import '../widgets/search_suggestions.dart';
import '../../../shared/widgets/enhanced_business_card.dart';
import '../../../shared/widgets/enhanced_search_bar.dart';
import '../../../shared/widgets/filter_chips.dart';
import '../../../core/theme/app_theme.dart';

class EnhancedSearchScreen extends StatefulWidget {
  final String? initialQuery;
  final int? categoryId;

  const EnhancedSearchScreen({
    super.key,
    this.initialQuery,
    this.categoryId,
  });

  @override
  State<EnhancedSearchScreen> createState() => _EnhancedSearchScreenState();
}

class _EnhancedSearchScreenState extends State<EnhancedSearchScreen> {
  final _searchController = TextEditingController();
  bool _showSuggestions = false;

  @override
  void initState() {
    super.initState();
    
    // Set initial query if provided
    if (widget.initialQuery != null) {
      _searchController.text = widget.initialQuery!;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<SearchNotifier>().search(widget.initialQuery!);
      });
    }
    
    // Set initial category filter if provided
    if (widget.categoryId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<SearchNotifier>().searchByCategory(widget.categoryId!);
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.gray100,
      appBar: AppBar(
        backgroundColor: AppTheme.white,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        title: Text(
          'Search',
          style: AppTheme.h3.copyWith(color: AppTheme.gray900),
        ),
      ),
      body: Column(
        children: [
          // Search Bar
          EnhancedSearchBar(
            initialValue: _searchController.text,
            onChanged: (query) {
              _searchController.text = query;
              setState(() {
                _showSuggestions = query.isNotEmpty;
              });
              context.read<SearchNotifier>().search(query);
            },
            onFilterTap: _showFilterBottomSheet,
            activeFiltersCount: context.watch<SearchNotifier>().activeFilterCount,
            autofocus: widget.initialQuery == null,
          ),
          
          // Active Filters
          Consumer<SearchNotifier>(
            builder: (context, searchNotifier, child) {
              if (!searchNotifier.filters.hasActiveFilters) {
                return const SizedBox.shrink();
              }
              
              return _buildActiveFilters(searchNotifier);
            },
          ),
          
          // Results Header (Sort and View Mode)
          Consumer<SearchNotifier>(
            builder: (context, searchNotifier, child) {
              if (searchNotifier.initialSearch || searchNotifier.results.isEmpty) {
                return const SizedBox.shrink();
              }
              
              return _buildResultsHeader(searchNotifier);
            },
          ),
          
          // Content
          Expanded(
            child: Consumer<SearchNotifier>(
              builder: (context, searchNotifier, child) {
                if (searchNotifier.isLoading) {
                  return _buildLoadingState();
                }

                if (searchNotifier.errorMessage != null) {
                  return _buildErrorState(searchNotifier.errorMessage!);
                }

                if (searchNotifier.initialSearch) {
                  return _showSuggestions
                      ? SearchSuggestions(
                          query: _searchController.text,
                          onSuggestionTap: (suggestion) {
                            _searchController.text = suggestion;
                            setState(() {
                              _showSuggestions = false;
                            });
                            context.read<SearchNotifier>().search(suggestion);
                          },
                        )
                      : const SearchInitialState();
                }

                if (searchNotifier.results.isEmpty) {
                  return _buildEmptyState();
                }

                return _buildResultsList(searchNotifier);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveFilters(SearchNotifier searchNotifier) {
    final filters = <FilterChipData>[];
    
    // Add category filters
    if (searchNotifier.filters.categoryIds.isNotEmpty) {
      filters.add(FilterChipData(
        id: 'categories',
        label: '${searchNotifier.filters.categoryIds.length} Categories',
        isSelected: true,
        showCloseButton: true,
      ));
    }
    
    // Add distance filter
    if (searchNotifier.filters.radiusKm != null) {
      filters.add(FilterChipData(
        id: 'radius',
        label: 'Within ${searchNotifier.filters.radiusKm}km',
        isSelected: true,
        showCloseButton: true,
        selectedColor: AppTheme.distanceBlue,
      ));
    }
    
    // Add rating filter
    if (searchNotifier.filters.minRating != null) {
      filters.add(FilterChipData(
        id: 'rating',
        label: '${searchNotifier.filters.minRating}+ Stars',
        isSelected: true,
        showCloseButton: true,
        selectedColor: AppTheme.goldStar,
      ));
    }
    
    // Add price filters
    if (searchNotifier.filters.priceRanges.isNotEmpty) {
      filters.add(FilterChipData(
        id: 'price',
        label: searchNotifier.filters.priceRanges.join(', '),
        isSelected: true,
        showCloseButton: true,
      ));
    }
    
    // Add quick filters
    if (searchNotifier.filters.openNow == true) {
      filters.add(FilterChipData(
        id: 'open_now',
        label: 'Open Now',
        isSelected: true,
        showCloseButton: true,
        selectedColor: AppTheme.successGreen,
      ));
    }
    
    if (searchNotifier.filters.hasProducts == true) {
      filters.add(FilterChipData(
        id: 'has_products',
        label: 'Has Products',
        isSelected: true,
        showCloseButton: true,
      ));
    }
    
    if (searchNotifier.filters.hasServices == true) {
      filters.add(FilterChipData(
        id: 'has_services',
        label: 'Has Services',
        isSelected: true,
        showCloseButton: true,
      ));
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.sm),
      child: FilterChipList(
        filters: filters,
        onFilterTap: (filterId) {
          searchNotifier.removeFilter(filterId);
        },
      ),
    );
  }

  Widget _buildResultsHeader(SearchNotifier searchNotifier) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.lg,
        vertical: AppTheme.sm,
      ),
      decoration: const BoxDecoration(
        color: AppTheme.white,
        border: Border(bottom: BorderSide(color: AppTheme.gray300)),
      ),
      child: Row(
        children: [
          Text(
            '${searchNotifier.results.length} results',
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray700),
          ),
          const Spacer(),
          // Sort Button
          TextButton.icon(
            onPressed: _showSortBottomSheet,
            icon: const Icon(FeatherIcons.arrowUpDown, size: 16),
            label: Text(searchNotifier.filters.sortBy.displayName),
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.gray700,
              textStyle: AppTheme.bodySmall,
            ),
          ),
          const SizedBox(width: AppTheme.sm),
          // View Mode Toggle
          IconButton(
            onPressed: () {
              final newMode = searchNotifier.filters.viewMode == ViewMode.list
                  ? ViewMode.grid
                  : ViewMode.list;
              searchNotifier.setViewMode(newMode);
            },
            icon: Icon(
              searchNotifier.filters.viewMode == ViewMode.list
                  ? FeatherIcons.grid
                  : FeatherIcons.list,
              size: 20,
            ),
            color: AppTheme.gray700,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.sm),
      itemCount: 5,
      itemBuilder: (context, index) => _buildBusinessShimmer(),
    );
  }

  Widget _buildBusinessShimmer() {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppTheme.lg,
        vertical: AppTheme.xs,
      ),
      height: 120,
      decoration: BoxDecoration(
        color: AppTheme.gray300,
        borderRadius: BorderRadius.circular(AppTheme.md),
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.xxxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              FeatherIcons.alertCircle,
              size: 64,
              color: AppTheme.errorRed,
            ),
            const SizedBox(height: AppTheme.lg),
            Text(
              'Oops! Something went wrong',
              style: AppTheme.h4.copyWith(color: AppTheme.errorRed),
            ),
            const SizedBox(height: AppTheme.sm),
            Text(
              error,
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppTheme.lg),
            ElevatedButton(
              onPressed: () {
                context.read<SearchNotifier>().search(_searchController.text);
              },
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.xxxl),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              FeatherIcons.search,
              size: 64,
              color: AppTheme.gray400,
            ),
            const SizedBox(height: AppTheme.lg),
            Text(
              'No results found',
              style: AppTheme.h4.copyWith(color: AppTheme.gray700),
            ),
            const SizedBox(height: AppTheme.sm),
            Text(
              'Try adjusting your search or filters to find what you\'re looking for.',
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultsList(SearchNotifier searchNotifier) {
    if (searchNotifier.filters.viewMode == ViewMode.grid) {
      return _buildGridView(searchNotifier.results);
    } else {
      return _buildListView(searchNotifier.results);
    }
  }

  Widget _buildListView(List results) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.sm),
      itemCount: results.length,
      itemBuilder: (context, index) {
        return EnhancedBusinessCard(business: results[index]);
      },
    );
  }

  Widget _buildGridView(List results) {
    return GridView.builder(
      padding: const EdgeInsets.all(AppTheme.lg),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: AppTheme.md,
        mainAxisSpacing: AppTheme.md,
      ),
      itemCount: results.length,
      itemBuilder: (context, index) {
        // TODO: Create a grid version of business card
        return EnhancedBusinessCard(business: results[index]);
      },
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FilterBottomSheet(
        initialFilters: context.read<SearchNotifier>().filters,
      ),
    );
  }

  void _showSortBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => SortBottomSheet(
        currentSort: context.read<SearchNotifier>().filters.sortBy,
      ),
    );
  }
}
