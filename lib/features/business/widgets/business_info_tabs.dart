import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';

import '../domain/business_detail_model.dart';
import '../../../core/theme/app_theme.dart';
import 'business_hours_display.dart';
import 'products_services_display.dart';

class BusinessInfoTabs extends StatelessWidget {
  final BusinessDetailModel business;
  final TabController tabController;

  const BusinessInfoTabs({
    super.key,
    required this.business,
    required this.tabController,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppTheme.white,
      child: Column(
        children: [
          // Tab Bar
          Container(
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: AppTheme.gray300)),
            ),
            child: TabBar(
              controller: tabController,
              labelColor: AppTheme.primaryPurple,
              unselectedLabelColor: AppTheme.gray500,
              indicatorColor: AppTheme.primaryPurple,
              labelStyle: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600),
              unselectedLabelStyle: AppTheme.bodyMedium,
              tabs: [
                Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(FeatherIcons.info, size: 16),
                      const SizedBox(width: AppTheme.xs),
                      const Text('Overview'),
                    ],
                  ),
                ),
                if (business.hasProducts)
                  Tab(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(FeatherIcons.package, size: 16),
                        const SizedBox(width: AppTheme.xs),
                        const Text('Menu'),
                      ],
                    ),
                  ),
                if (business.hasServices)
                  Tab(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(FeatherIcons.tool, size: 16),
                        const SizedBox(width: AppTheme.xs),
                        const Text('Services'),
                      ],
                    ),
                  ),
                Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(FeatherIcons.star, size: 16),
                      const SizedBox(width: AppTheme.xs),
                      Text('Reviews (${business.reviewCount})'),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Tab Content
          SizedBox(
            height: 600, // Fixed height for tab content
            child: TabBarView(
              controller: tabController,
              children: [
                // Overview Tab
                _buildOverviewTab(),
                
                // Products Tab (if available)
                if (business.hasProducts)
                  ProductsServicesDisplay(
                    products: business.products,
                    services: const [],
                    showProducts: true,
                  ),
                
                // Services Tab (if available)
                if (business.hasServices)
                  ProductsServicesDisplay(
                    products: const [],
                    services: business.services,
                    showProducts: false,
                  ),
                
                // Reviews Tab
                _buildReviewsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Description
          if (business.description.isNotEmpty) ...[
            Text(
              'About',
              style: AppTheme.h5.copyWith(color: AppTheme.gray900),
            ),
            const SizedBox(height: AppTheme.sm),
            Text(
              business.description,
              style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray700),
            ),
            const SizedBox(height: AppTheme.xxl),
          ],

          // Hours
          BusinessHoursDisplay(hours: business.hours),
          const SizedBox(height: AppTheme.xxl),

          // Contact Information
          _buildContactSection(),
          const SizedBox(height: AppTheme.xxl),

          // Categories
          if (business.categories.isNotEmpty) ...[
            _buildCategoriesSection(),
            const SizedBox(height: AppTheme.xxl),
          ],

          // Photos
          if (business.media.isNotEmpty) ...[
            _buildPhotosSection(),
          ],
        ],
      ),
    );
  }

  Widget _buildContactSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Contact',
          style: AppTheme.h5.copyWith(color: AppTheme.gray900),
        ),
        const SizedBox(height: AppTheme.md),
        
        // Phone
        if (business.phoneNumber.isNotEmpty)
          _buildContactItem(
            icon: FeatherIcons.phone,
            label: 'Phone',
            value: business.phoneNumber,
          ),
        
        // Website
        if (business.websiteUrl != null && business.websiteUrl!.isNotEmpty)
          _buildContactItem(
            icon: FeatherIcons.globe,
            label: 'Website',
            value: business.websiteUrl!,
          ),
        
        // Address
        _buildContactItem(
          icon: FeatherIcons.mapPin,
          label: 'Address',
          value: business.address,
        ),
      ],
    );
  }

  Widget _buildContactItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.sm),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: AppTheme.gray500,
            size: 16,
          ),
          const SizedBox(width: AppTheme.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.gray500,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray900),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Categories',
          style: AppTheme.h5.copyWith(color: AppTheme.gray900),
        ),
        const SizedBox(height: AppTheme.md),
        Wrap(
          spacing: AppTheme.sm,
          runSpacing: AppTheme.sm,
          children: business.categories.map((category) {
            return Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.md,
                vertical: AppTheme.xs,
              ),
              decoration: BoxDecoration(
                color: AppTheme.gray100,
                borderRadius: BorderRadius.circular(AppTheme.lg),
                border: Border.all(color: AppTheme.gray300),
              ),
              child: Text(
                category.name,
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.gray700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPhotosSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Photos',
          style: AppTheme.h5.copyWith(color: AppTheme.gray900),
        ),
        const SizedBox(height: AppTheme.md),
        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: business.media.length,
            itemBuilder: (context, index) {
              final media = business.media[index];
              return Container(
                margin: const EdgeInsets.only(right: AppTheme.sm),
                width: 100,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppTheme.sm),
                  color: AppTheme.gray100,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(AppTheme.sm),
                  child: Image.network(
                    media.url,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(
                        FeatherIcons.image,
                        color: AppTheme.gray500,
                      );
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildReviewsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Reviews summary
          _buildReviewsSummary(),
          const SizedBox(height: AppTheme.xxl),
          
          // Reviews list
          if (business.reviews.isNotEmpty) ...[
            ...business.reviews.map((review) => _buildReviewItem(review)),
          ] else ...[
            Center(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.xxxl),
                child: Column(
                  children: [
                    const Icon(
                      FeatherIcons.star,
                      size: 48,
                      color: AppTheme.gray400,
                    ),
                    const SizedBox(height: AppTheme.lg),
                    Text(
                      'No reviews yet',
                      style: AppTheme.h5.copyWith(color: AppTheme.gray700),
                    ),
                    const SizedBox(height: AppTheme.sm),
                    Text(
                      'Be the first to review this business',
                      style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray500),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildReviewsSummary() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.lg),
      decoration: BoxDecoration(
        color: AppTheme.gray100,
        borderRadius: BorderRadius.circular(AppTheme.md),
      ),
      child: Row(
        children: [
          Column(
            children: [
              Text(
                business.averageRating.toStringAsFixed(1),
                style: AppTheme.h1.copyWith(
                  color: AppTheme.gray900,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppTheme.xs),
              Text(
                '${business.reviewCount} reviews',
                style: AppTheme.bodySmall.copyWith(color: AppTheme.gray500),
              ),
            ],
          ),
          const SizedBox(width: AppTheme.lg),
          Expanded(
            child: Column(
              children: [
                // TODO: Add rating distribution bars
                Text(
                  'Rating distribution coming soon',
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.gray500),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewItem(review) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.lg),
      padding: const EdgeInsets.all(AppTheme.lg),
      decoration: BoxDecoration(
        color: AppTheme.gray100,
        borderRadius: BorderRadius.circular(AppTheme.md),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: AppTheme.primaryPurple,
                child: Text(
                  'U', // TODO: Use actual user initial
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: AppTheme.sm),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'User Name', // TODO: Use actual user name
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.gray900,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      '2 days ago', // TODO: Use actual date
                      style: AppTheme.bodySmall.copyWith(color: AppTheme.gray500),
                    ),
                  ],
                ),
              ),
              // Rating stars
              Row(
                children: List.generate(5, (index) {
                  return Icon(
                    Icons.star,
                    size: 16,
                    color: index < 4 ? AppTheme.goldStar : AppTheme.gray300,
                  );
                }),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.sm),
          Text(
            'Great service and food!', // TODO: Use actual review text
            style: AppTheme.bodyMedium.copyWith(color: AppTheme.gray700),
          ),
        ],
      ),
    );
  }
}
